[gd_scene load_steps=3 format=3 uid="uid://c8x7y2qm8qrst"]

[ext_resource type="PackedScene" uid="uid://bx8k7m2n9ap1q" path="res://Scenes/Collectables/BaseCollectable.tscn" id="1_base"]
[ext_resource type="Texture2D" uid="uid://ba4pw6xs2250w" path="res://Assets/kenney_simple-space/PNG/Default/star_medium.png" id="2_star"]

[node name="StarCollectable" instance=ExtResource("1_base")]
collectable_type = 0
point_value = 100
orbit_radius = 120.0
orbit_speed = 1.5
collection_name = "Star"

[node name="Sprite2D" parent="." index="0"]
modulate = Color(1, 1, 0.8, 1)
scale = Vector2(1.92479, 1.92479)
texture = ExtResource("2_star")

[node name="CollectionParticles" parent="." index="2"]
color = Color(1, 1, 0, 1)
