[gd_scene load_steps=20 format=3 uid="uid://d0j2xqgs8463m"]

[ext_resource type="Script" uid="uid://bkgfm5k1vexr" path="res://Scripts/planet.gd" id="1_ej51v"]
[ext_resource type="Shader" uid="uid://dttlrh3oqhy7w" path="res://Assets/Shaders/Atmosphere.gdshader" id="2_ej51v"]
[ext_resource type="Texture2D" uid="uid://b6y5iuktvcwd4" path="res://Assets/kenney_planets/Planets/planet00.png" id="2_m5833"]
[ext_resource type="Texture2D" uid="uid://cttrn2nakn67e" path="res://Assets/Circle.png" id="2_r86au"]
[ext_resource type="Texture2D" uid="uid://cgt4c8whbpa4p" path="res://Assets/kenney_planets/Planets/planet01.png" id="3_q5dxo"]
[ext_resource type="Script" uid="uid://cqvosktsb48d3" path="res://Scripts/tools/planet_atmo.gd" id="4_aogve"]
[ext_resource type="Texture2D" uid="uid://bk7d1uwiwf5vm" path="res://Assets/kenney_planets/Planets/planet02.png" id="4_pgxtg"]
[ext_resource type="Texture2D" uid="uid://b3dfuop6n5fr2" path="res://Assets/kenney_planets/Planets/planet03.png" id="5_ueupv"]
[ext_resource type="Texture2D" uid="uid://ef8vjx463bnj" path="res://Assets/kenney_planets/Planets/planet04.png" id="6_vwein"]
[ext_resource type="Texture2D" uid="uid://bcmnm1avyhgv8" path="res://Assets/kenney_planets/Planets/planet05.png" id="7_gebx1"]
[ext_resource type="Texture2D" uid="uid://x45e1e0ufjlm" path="res://Assets/kenney_planets/Planets/planet06.png" id="8_04ox6"]
[ext_resource type="Texture2D" uid="uid://dgy0qlfsppkme" path="res://Assets/kenney_planets/Planets/planet07.png" id="9_roqmg"]
[ext_resource type="Texture2D" uid="uid://fhqpobm2xyfy" path="res://Assets/kenney_planets/Planets/planet08.png" id="10_qbjmh"]
[ext_resource type="Texture2D" uid="uid://d2lxucwpeaiew" path="res://Assets/kenney_planets/Planets/planet09.png" id="11_eci5g"]
[ext_resource type="Script" path="res://Scripts/rand_sprite.gd" id="12_q5dxo"]
[ext_resource type="Script" path="res://Scenes/CollectableSpawner.gd" id="16_q5dxo"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_aogve"]

[sub_resource type="CircleShape2D" id="CircleShape2D_effgs"]
radius = 207.002

[sub_resource type="ShaderMaterial" id="ShaderMaterial_umgws"]
shader = ExtResource("2_ej51v")
shader_parameter/center = Vector2(0.5, 0.5)
shader_parameter/size = 1.0
shader_parameter/squishness = Vector2(1, 1)
shader_parameter/color1 = Color(0, 1, 0, 0)
shader_parameter/color2 = Color(0, 0, 0, 1)
shader_parameter/color3 = Color(0, 1, 0, 0.0313726)
shader_parameter/color4 = Color(0, 0, 0, 1)
shader_parameter/color5 = Color(0, 1, 0, 0.243137)
shader_parameter/color6 = Color(0, 0, 0, 1)
shader_parameter/step1 = 0.159
shader_parameter/step2 = 0.182
shader_parameter/step3 = 0.233
shader_parameter/step4 = 0.264
shader_parameter/step5 = 0.265
shader_parameter/step6 = 0.266

[node name="Planet2" type="Area2D"]
script = ExtResource("1_ej51v")

[node name="Sprite" type="Sprite2D" parent="."]
script = ExtResource("12_q5dxo")
PlanetSprites = Array[Texture2D]([ExtResource("2_m5833"), ExtResource("3_q5dxo"), ExtResource("4_pgxtg"), ExtResource("5_ueupv"), ExtResource("6_vwein"), ExtResource("7_gebx1"), ExtResource("8_04ox6"), ExtResource("9_roqmg"), ExtResource("10_qbjmh"), ExtResource("11_eci5g")])

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
material = SubResource("ShaderMaterial_aogve")
shape = SubResource("CircleShape2D_effgs")

[node name="Sprite2D" type="Sprite2D" parent="CollisionShape2D"]
material = SubResource("ShaderMaterial_umgws")
scale = Vector2(0.602626, 0.602626)
texture = ExtResource("2_r86au")
script = ExtResource("4_aogve")

[node name="AnimatableBody2D" type="AnimatableBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="AnimatableBody2D"]

[node name="Node2D" type="Node2D" parent="."]
script = ExtResource("16_q5dxo")
OrbitRangeMax = 0.0
OrbitRangeMin = 0.0

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
[connection signal="body_exited" from="." to="." method="_on_body_exited"]
