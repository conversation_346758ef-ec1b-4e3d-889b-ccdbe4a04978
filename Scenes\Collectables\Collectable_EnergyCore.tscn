[gd_scene load_steps=3 format=3 uid="uid://c1lsn0rglq41p"]

[ext_resource type="PackedScene" uid="uid://bx8k7m2n9ap1q" path="res://Scenes/Collectables/BaseCollectable.tscn" id="1_base"]
[ext_resource type="Texture2D" uid="uid://cx06de664828k" path="res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Building/spaceBuilding_002.png" id="2_wdw6q"]

[node name="EnergyCoreCollectable" instance=ExtResource("1_base")]
collectable_type = 3
point_value = 100
orbit_radius = 250.0
orbit_speed = 0.4
collection_name = "Goods"

[node name="Sprite2D" parent="." index="0"]
texture = ExtResource("2_wdw6q")

[node name="CollectionParticles" parent="." index="2"]
color = Color(0, 1, 0, 1)
