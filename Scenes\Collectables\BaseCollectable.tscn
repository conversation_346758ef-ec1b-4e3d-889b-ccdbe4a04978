[gd_scene load_steps=4 format=3 uid="uid://bx8k7m2oaap1q"]

[ext_resource type="Script" uid="uid://dbms3atxo2hfg" path="res://Scripts/Collectable.gd" id="1_collectable"]
[ext_resource type="Script" uid="uid://deew7a1ghrogw" path="res://Scripts/PlayerAudioHandler.gd" id="2_audio"]

[sub_resource type="CircleShape2D" id="CircleShape2D_base"]
radius = 50.0

[node name="BaseCollectable" type="Area2D"]
script = ExtResource("1_collectable")
collectable_type = 0
point_value = 100
orbit_radius = 150.0
orbit_speed = 1.0
collection_name = "Collectable"

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(1.0, 1.0)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_base")

[node name="CollectionParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 20
lifetime = 1.0
explosiveness = 1.0
direction = Vector2(0, -1)
gravity = Vector2(0, 60)
initial_velocity_min = 50.0
initial_velocity_max = 100.0
scale_amount_min = 0.5
scale_amount_max = 1.5
color = Color.WHITE

[node name="PlayerAudioHandler" type="Node2D" parent="."]
script = ExtResource("2_audio")
