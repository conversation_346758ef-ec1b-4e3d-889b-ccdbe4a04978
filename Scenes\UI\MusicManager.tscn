[gd_scene load_steps=7 format=3 uid="uid://b5vfoako1gnde"]

[ext_resource type="Script" path="res://Scenes/UI/music_manager.gd" id="1_u0apm"]
[ext_resource type="Script" uid="uid://ehkw6w2xh2bq" path="res://addons/audio_manager/audio_manager_omni.gd" id="2_6j6m3"]
[ext_resource type="AudioStream" uid="uid://bgpj881la2ljv" path="res://Assets/Audio/Music/GMTK Chill Track.wav" id="3_u0apm"]
[ext_resource type="AudioStream" uid="uid://l0sx6418is77" path="res://Assets/Audio/SFX/UIClickSFX.wav" id="4_6j6m3"]

[sub_resource type="Resource" id="Resource_cwhqb"]
script = ExtResource("2_6j6m3")
audio_name = "background_music"
audio_stream = ExtResource("3_u0apm")
use_clipper = false
start_time = 0.0
end_time = 0.0
volume_db = -30.25
pitch_scale = 1.0
mix_target = 0
loop = true
loop_offset = 0.0
auto_play = true
max_polyphony = 1
bus = &"Music"
playback_type = 0
metadata/_custom_type_script = "uid://ehkw6w2xh2bq"

[sub_resource type="Resource" id="Resource_f2cq6"]
script = ExtResource("2_6j6m3")
audio_name = "button_click"
audio_stream = ExtResource("4_6j6m3")
use_clipper = false
start_time = 0.0
end_time = 0.0
volume_db = 0.0
pitch_scale = 1.0
mix_target = 0
loop = false
loop_offset = 0.0
auto_play = false
max_polyphony = 1
bus = &"Master"
playback_type = 0
metadata/_custom_type_script = "uid://ehkw6w2xh2bq"

[node name="AudioManager" type="Node"]
script = ExtResource("1_u0apm")
audios_omni = Array[ExtResource("2_6j6m3")]([SubResource("Resource_cwhqb"), SubResource("Resource_f2cq6")])
metadata/_custom_type_script = "uid://jdiu05rj7h3d"
