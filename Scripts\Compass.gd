extends Control

@onready var home_icon: TextureRect = %HomeIcon
@onready var planet_icons_container: Control = %PlanetIcons
@onready var player_dot: TextureRect = %PlayerDot

var player: RigidBody2D
var home_planet: Area2D
var planets: Array[Area2D] = []
var planet_icons: Array[TextureRect] = []
var shop_icons: Array[TextureRect] = []
var collectable_icons: Array[Array] = []  # Array of arrays, one per planet

# --- Minimap Settings ---
# The radius of the map circle in pixels.
@export var map_radius: float = 50.0
# How much to "zoom" the map. Smaller numbers = more zoomed out.
@export var map_scale: float = 0.01
# Range for showing planets as "in range"
@export var planet_range_threshold: float = 2000.0

# Icon textures
var planet_texture: Texture2D = preload("res://Assets/kenney_simple-space/PNG/Default/meteor_small.png")
var shop_texture: Texture2D = preload("res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_021.png")
var player_texture: Texture2D = preload("res://Assets/Circle.png")
var collectable_texture: Texture2D = preload("res://Assets/kenney_simple-space/PNG/Default/star_small.png")
func _ready():

	var background: TextureRect = $Background
	background.modulate = Color(0.1, 0.1, 0.2, 0.8)

	# Set up player dot in center
	setup_player_dot()

func setup_compass(player_ref: RigidBody2D, home_ref: Area2D, planets_ref: Array[Area2D]):
	player = player_ref
	home_planet = home_ref
	planets = planets_ref
	create_planet_icons()
	create_shop_icons()
	create_collectable_icons()

func setup_player_dot():
	# This creates the player dot in the center of the minimap
	if not player_dot:
		return

	player_dot.texture = player_texture
	player_dot.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
	player_dot.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	player_dot.size = Vector2(8, 8)
	player_dot.pivot_offset = player_dot.size / 2
	player_dot.modulate = Color.WHITE

	# Position in center of compass
	var compass_center = $CompassCenter
	player_dot.position = compass_center.size / 2.0 - player_dot.size / 2.0

func create_planet_icons():
	# Clear existing icons
	for icon in planet_icons:
		if is_instance_valid(icon):
			icon.queue_free()
	planet_icons.clear()

	# Create icons for each planet
	for _i in range(planets.size()):
		var planet_icon = TextureRect.new()
		planet_icon.texture = planet_texture
		planet_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		planet_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		planet_icon.size = Vector2(16, 16)
		planet_icon.pivot_offset = planet_icon.size / 2
		planet_icons_container.add_child(planet_icon)
		planet_icons.append(planet_icon)

func create_shop_icons():
	# Clear existing shop icons
	for icon in shop_icons:
		if is_instance_valid(icon):
			icon.queue_free()
	shop_icons.clear()

	# Create shop icons for planets that have shops (HomePlanet)
	for i in range(planets.size()):
		var planet = planets[i]
		var shop_icon = TextureRect.new()
		shop_icon.texture = shop_texture
		shop_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		shop_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		shop_icon.size = Vector2(12, 12)
		shop_icon.pivot_offset = shop_icon.size / 2
		shop_icon.modulate = Color.ORANGE

		# Only show shop icon if planet has a shop (is HomePlanet)
		if planet is HomePlanet:
			shop_icon.visible = true
		else:
			shop_icon.visible = false

		planet_icons_container.add_child(shop_icon)
		shop_icons.append(shop_icon)

func create_collectable_icons():
	# Clear existing collectable icons
	for planet_collectables in collectable_icons:
		for icon in planet_collectables:
			if is_instance_valid(icon):
				icon.queue_free()
	collectable_icons.clear()

	# Create collectable icon arrays for each planet
	for _i in range(planets.size()):
		collectable_icons.append([])

func _process(_delta):
	if not is_instance_valid(player) or not is_instance_valid(home_planet):
		return
	
	update_map()

func update_map():
	var player_pos = player.global_position

	update_icon_position(home_icon, home_planet.global_position, player_pos)

	for i in range(min(planets.size(), planet_icons.size())):
		if is_instance_valid(planets[i]) and is_instance_valid(planet_icons[i]):
			update_icon_position(planet_icons[i], planets[i].global_position, player_pos)

			# Update shop icon if it exists
			if i < shop_icons.size() and is_instance_valid(shop_icons[i]):
				update_shop_icon_position(shop_icons[i], planets[i], player_pos)

			# Update collectable icons for this planet
			if i < collectable_icons.size():
				update_collectable_icons_for_planet(i, planets[i], player_pos)

func update_icon_position(icon: TextureRect, world_pos: Vector2, player_pos: Vector2):
	# 1. Get the vector from the player to the object in the game world.
	var relative_pos = world_pos - player_pos
	
	# 2. Scale that huge world vector down to our tiny map scale.
	var map_pos = relative_pos * map_scale
	
	# 3. If the object is outside our map's radius, clamp it to the edge.
	if map_pos.length() > map_radius:
		map_pos = map_pos.normalized() * map_radius

	# 4. Set the icon's position on the map.
	icon.position = map_pos + $CompassCenter.size / 2.0
	
	# --- Visuals (Color & Size) ---
	var distance = player_pos.distance_to(world_pos)
	var scale_factor = clamp(2000.0 / distance, 0.6, 1.5) # Adjust sizing
	icon.scale = Vector2(scale_factor, scale_factor)

	if icon == home_icon:
		icon.modulate = Color.CYAN
		var pulse = sin(Time.get_ticks_msec() / 200.0) * 0.1 + 1.0
		icon.scale *= pulse
	else:
		# Show range indicators - green for in range, yellow for medium, red for far
		if distance < planet_range_threshold:
			icon.modulate = Color.GREEN  # In range
		elif distance < planet_range_threshold * 2:
			icon.modulate = Color.YELLOW  # Medium range
		else:
			icon.modulate = Color.RED  # Out of range

func update_shop_icon_position(shop_icon: TextureRect, planet: Area2D, player_pos: Vector2):
	# Only show shop icon if planet has a shop
	if not planet is HomePlanet:
		shop_icon.visible = false
		return

	shop_icon.visible = true

	# Position shop icon slightly offset from planet icon
	var planet_world_pos = planet.global_position
	var relative_pos = planet_world_pos - player_pos
	var map_pos = relative_pos * map_scale

	# Clamp to map radius
	if map_pos.length() > map_radius:
		map_pos = map_pos.normalized() * map_radius

	# Offset shop icon slightly from planet position
	var offset = Vector2(8, -8)  # Small offset to show shop icon near planet
	shop_icon.position = map_pos + $CompassCenter.size / 2.0 + offset

	# Color shop icon based on distance
	var distance = player_pos.distance_to(planet_world_pos)
	if distance < planet_range_threshold:
		shop_icon.modulate = Color.ORANGE  # Shop in range
	else:
		shop_icon.modulate = Color(0.8, 0.4, 0.0, 0.7)  # Shop out of range (dimmed)

func update_collectable_icons_for_planet(planet_index: int, planet: Area2D, player_pos: Vector2):
	# Get all collectables that orbit this planet
	var collectables = get_tree().get_nodes_in_group("collectables")
	var planet_collectables = []

	for collectable in collectables:
		if is_instance_valid(collectable) and collectable.has_method("get") and collectable.get("planet_reference") == planet:
			if not collectable.is_collected:
				planet_collectables.append(collectable)

	# Get current icons for this planet
	var current_icons = collectable_icons[planet_index]

	# Remove excess icons if there are fewer collectables now
	while current_icons.size() > planet_collectables.size():
		var icon = current_icons.pop_back()
		if is_instance_valid(icon):
			icon.queue_free()

	# Add new icons if there are more collectables now
	while current_icons.size() < planet_collectables.size():
		var new_icon = TextureRect.new()
		new_icon.texture = collectable_texture
		new_icon.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		new_icon.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		new_icon.size = Vector2(6, 6)
		new_icon.pivot_offset = new_icon.size / 2
		new_icon.modulate = Color.YELLOW
		planet_icons_container.add_child(new_icon)
		current_icons.append(new_icon)

	# Position collectable icons around the planet
	var planet_world_pos = planet.global_position
	var relative_pos = planet_world_pos - player_pos
	var map_pos = relative_pos * map_scale

	# Clamp to map radius
	if map_pos.length() > map_radius:
		map_pos = map_pos.normalized() * map_radius

	var base_position = map_pos + $CompassCenter.size / 2.0

	# Arrange collectable icons in a small circle around the planet
	for i in range(current_icons.size()):
		if is_instance_valid(current_icons[i]):
			var angle = (i * 2 * PI) / max(current_icons.size(), 1)
			var offset = Vector2(cos(angle), sin(angle)) * 10  # Small circle around planet
			current_icons[i].position = base_position + offset
			current_icons[i].visible = true
