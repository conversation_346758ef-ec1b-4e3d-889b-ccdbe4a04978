[gd_scene load_steps=3 format=3 uid="uid://bpykgsig0p7c4"]

[ext_resource type="PackedScene" uid="uid://bx8k7m2n9ap1q" path="res://Scenes/Collectables/BaseCollectable.tscn" id="1_base"]
[ext_resource type="Texture2D" uid="uid://dwrxcriyojn1o" path="res://Assets/kenney_space-shooter-extension/PNG/Sprites/Station/spaceStation_022.png" id="2_7ii5o"]

[node name="SatelliteCollectable" instance=ExtResource("1_base")]
collectable_type = 1
point_value = 100
orbit_radius = 180.0
orbit_speed = 0.8
collection_name = "Satellite"

[node name="Sprite2D" parent="." index="0"]
modulate = Color(0.8, 1, 1, 1)
texture = ExtResource("2_7ii5o")

[node name="CollectionParticles" parent="." index="2"]
color = Color(0, 1, 1, 1)
