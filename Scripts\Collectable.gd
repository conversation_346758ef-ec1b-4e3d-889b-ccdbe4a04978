extends Area2D
class_name Collectable

# Collectable types enum
enum CollectableType {
	STAR,
	SATELLITE,
	CRYSTAL,
	ENERGY_CORE,
	METEOR_FRAGMENT
}

# Signals
signal collected(collectable: Collectable)

# Export variables
@export var collectable_type: CollectableType = CollectableType.STAR
@export var point_value: int = 100
@export var orbit_radius: float = 150.0
@export var orbit_speed: float = 1.0
@export var collection_name: String = "Star"

# Loop collection system
@export var loop_completion_percentage: float = 0.85  # Percentage of loop needed to collect (85% default)
@export var enable_loop_collection: bool = true  # Enable collection by looping around planet

# Value degradation system
@export var value_degradation_interval: float = 1.0  # Time in seconds between value reductions
@export var value_degradation_amount: int = 1  # Amount to reduce value by each interval
@export var minimum_value: int = 1  # Minimum value the collectable can have

# sound handler
@onready var audioHandler: PlayerAudioHandler = $PlayerAudioHandler

# Internal variables
var orbit_center: Vector2
var orbit_angle: float = 0.0
var is_collected: bool = false
var planet_reference: Area2D

# Loop collection tracking variables
var player_loop_data: Dictionary = {}  # Tracks loop progress for each player
var initial_player_angle: float = 0.0
var loop_start_time: float = 0.0

# Value degradation variables
var original_point_value: int
var degradation_timer: float = 0.0

# Node references
@onready var sprite: Sprite2D = $Sprite2D
@onready var collection_area: CollisionShape2D = $CollisionShape2D
@onready var collection_particles: CPUParticles2D = $CollectionParticles

func _ready() -> void:
	# Add to collectables group
	add_to_group("collectables")

	# Connect to body entered signal for collection detection
	body_entered.connect(_on_body_entered)

	# Set up collection particles but don't emit yet
	if collection_particles:
		collection_particles.emitting = false
		setup_collection_particles()

	#orbit planet gets set externally now.
	# Find the nearest planet to orbit around
	#find_orbit_planet()

	# Store original point value for degradation system
	original_point_value = point_value

	# Set random starting angle
	orbit_angle = randf() * 2 * PI

func _physics_process(delta: float) -> void:
	if is_collected or not planet_reference:
		return

	# Update value degradation timer
	degradation_timer += delta
	if degradation_timer >= value_degradation_interval:
		degradation_timer = 0.0
		degrade_value()

	# Update orbit position
	orbit_angle += orbit_speed * delta
	if orbit_angle > 2 * PI:
		orbit_angle -= 2 * PI

	# Calculate new position based on orbit
	var orbit_offset = Vector2(cos(orbit_angle), sin(orbit_angle)) * orbit_radius
	global_position = orbit_center + orbit_offset

	# Track player loop progress if loop collection is enabled
	if enable_loop_collection:
		track_player_loops()

	# Rotate the sprite for visual effect
	sprite.rotation += orbit_speed * delta * 0.5

func find_orbit_planet(overridePlanet : BasePlanet = null) -> void:
	if(overridePlanet):
		planet_reference = overridePlanet
		orbit_center = overridePlanet.global_position
		return
	# Find the closest planet to orbit around
	var planets = get_tree().get_nodes_in_group("planets")
	if planets.is_empty():
		# Fallback: find any BasePlanet in the scene
		planets = find_all_planets(get_tree().current_scene)

	if planets.is_empty():
		print("Warning: No planets found for collectable to orbit!")
		return

	var closest_planet : BasePlanet = null
	var closest_distance = INF

	for planet in planets:
		var distance = global_position.distance_to(planet.global_position)
		if distance < closest_distance:
			closest_distance = distance
			closest_planet = planet

	if closest_planet:
		planet_reference = closest_planet
		orbit_center = closest_planet.global_position
		# Removed for dynamic spawning.
		
		# Adjust orbit radius based on distance to planet
		#var distance_to_planet = global_position.distance_to(orbit_center)
		#if distance_to_planet > 50:  # If we're far from the planet, use that distance 
			#orbit_radius = distance_to_planet

func find_all_planets(node: Node) -> Array:
	var planets = []
	if node is BasePlanet:
		planets.append(node)
	for child in node.get_children():
		planets.append_array(find_all_planets(child))
	return planets

func _on_body_entered(body: Node2D) -> void:
	if is_collected:
		return
		
	if body is Player:
		collect()

func collect() -> void:
	if is_collected:
		return
		
	is_collected = true
	audioHandler.PlaySoundAtGlobalPosition(Sounds.CollectableGet, global_position)
	# Play collection particles
	if collection_particles:
		collection_particles.emitting = true
	
	# Add score
	GameManager.add_score(point_value)
	
	# Emit collected signal
	collected.emit(self)
	
	# Hide the sprite and collision
	sprite.visible = false
	collection_area.set_deferred("disabled", true)
	
	# Wait for particles to finish, then remove
	await get_tree().create_timer(1.0).timeout
	queue_free()

#You can't dynamically load on a web build; you have to load at build time.
const STAR_PARTICLES = preload("res://Assets/kenney_simple-space/PNG/Default/star_small.png")

func setup_collection_particles() -> void:
	if not collection_particles:
		return
		
	# Configure particles based on collectable type
	match collectable_type:
		CollectableType.STAR:
			collection_particles.texture = STAR_PARTICLES
			collection_particles.amount = 20
			collection_particles.color = Color.YELLOW
		CollectableType.SATELLITE:
			collection_particles.amount = 15
			collection_particles.color = Color.CYAN
		CollectableType.CRYSTAL:
			collection_particles.amount = 25
			collection_particles.color = Color.MAGENTA
		CollectableType.ENERGY_CORE:
			collection_particles.amount = 30
			collection_particles.color = Color.GREEN
		CollectableType.METEOR_FRAGMENT:
			collection_particles.amount = 10
			collection_particles.color = Color.ORANGE
	
	# Common particle settings
	collection_particles.lifetime = 0.5
	collection_particles.explosiveness = 1.0
	collection_particles.direction = Vector2(0, -1)
	collection_particles.initial_velocity_min = 50.0
	collection_particles.initial_velocity_max = 100.0
	collection_particles.gravity = Vector2(0, 98)
	collection_particles.scale_amount_min = 0.5
	collection_particles.scale_amount_max = 1.5

func degrade_value() -> void:
	# Reduce the point value over time, but don't go below minimum
	if point_value > minimum_value:
		point_value = max(point_value - value_degradation_amount, minimum_value)

func get_collectable_info() -> Dictionary:
	return {
		"type": collectable_type,
		"name": collection_name,
		"points": point_value
	}

# Track player loop progress around the planet this collectable orbits
func track_player_loops() -> void:
	if not planet_reference:
		return

	# Find all players in the planet's gravity field
	var players_in_field = []
	if planet_reference.has_method("get") and planet_reference.get("bodies_in_gravity_field"):
		for body in planet_reference.bodies_in_gravity_field:
			if body is Player:
				players_in_field.append(body)

	# Track each player's loop progress
	for player in players_in_field:
		track_individual_player_loop(player)

# Track an individual player's loop progress
func track_individual_player_loop(player: Player) -> void:
	var player_id = player.get_instance_id()

	# Initialize tracking data if this is the first time seeing this player
	if not player_id in player_loop_data:
		initialize_player_loop_tracking(player)
		return

	var current_angle = get_angle_to_player_from_planet(player)
	var loop_data = player_loop_data[player_id]
	var last_angle = loop_data["last_angle"]

	# Calculate angle difference, handling wrap-around
	var angle_diff = current_angle - last_angle
	if angle_diff > PI:
		angle_diff -= 2 * PI
	elif angle_diff < -PI:
		angle_diff += 2 * PI

	# Update total rotation
	loop_data["total_rotation"] += angle_diff
	loop_data["last_angle"] = current_angle

	# Check if player completed enough of the loop to collect
	var completion_ratio = abs(loop_data["total_rotation"]) / (2 * PI)
	if completion_ratio >= loop_completion_percentage and not loop_data["has_collected"]:
		loop_data["has_collected"] = true
		collect()

# Initialize loop tracking for a player
func initialize_player_loop_tracking(player: Player) -> void:
	var player_id = player.get_instance_id()
	var initial_angle = get_angle_to_player_from_planet(player)
	player_loop_data[player_id] = {
		"last_angle": initial_angle,
		"total_rotation": 0.0,
		"has_collected": false,
		"start_time": Time.get_time_dict_from_system()["second"]
	}

# Get angle from planet center to player
func get_angle_to_player_from_planet(player: Player) -> float:
	if not planet_reference:
		return 0.0
	var direction = player.global_position - planet_reference.global_position
	return direction.angle()
