[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://bxcys6gyngoe6"]

[ext_resource type="Shader" uid="uid://eypjeee8ergs" path="res://Assets/Shaders/StarBackground2.gdshader" id="1_6wp6r"]

[resource]
shader = ExtResource("1_6wp6r")
shader_parameter/bg_color = Color(0, 0, 0, 1)
shader_parameter/offset = Vector2(0, 0)
shader_parameter/bigStarSlow = 2.0
shader_parameter/mediumStarSlow = 6.0
shader_parameter/smallStarSlow = 8.0
shader_parameter/smallStarAmount = 0.002
shader_parameter/mediumStarAmount = 0.01
shader_parameter/bigStarAmount = 0.02
