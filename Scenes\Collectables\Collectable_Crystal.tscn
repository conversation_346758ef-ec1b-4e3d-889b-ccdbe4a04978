[gd_scene load_steps=3 format=3 uid="uid://dkjlxh0onjytk"]

[ext_resource type="PackedScene" uid="uid://bx8k7m2n9ap1q" path="res://Scenes/Collectables/BaseCollectable.tscn" id="1_base"]
[ext_resource type="Texture2D" uid="uid://bpigwd5ie3c1p" path="res://Assets/kenney_simple-space/PNG/Default/meteor_detailedSmall.png" id="2_crystal"]

[node name="CrystalCollectable" instance=ExtResource("1_base")]
collectable_type = 2
point_value = 100
orbit_radius = 200.0
orbit_speed = 0.6
collection_name = "Crystal"

[node name="Sprite2D" parent="." index="0"]
modulate = Color(1, 0.5, 1, 1)
scale = Vector2(1.7653, 1.7653)
texture = ExtResource("2_crystal")

[node name="CollectionParticles" parent="." index="2"]
color = Color(1, 0, 1, 1)
