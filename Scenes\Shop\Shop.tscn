[gd_scene load_steps=4 format=3 uid="uid://bqm8x7y2n8k8r"]

[ext_resource type="Script" uid="uid://wtxgg31d4juy" path="res://Scripts/Shop/ShopManager.gd" id="1_shop"]
[ext_resource type="Theme" uid="uid://dvwgvp8u8xo0i" path="res://Scenes/UI/Themes/UI_Theme.tres" id="2_theme"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.8)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="ShopManager" type="CanvasLayer"]
script = ExtResource("1_shop")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_theme")

[node name="ShopPanel" type="Panel" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -250.0
offset_right = 300.0
offset_bottom = 250.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="Control/ShopPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderContainer" type="HBoxContainer" parent="Control/ShopPanel/VBoxContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="Control/ShopPanel/VBoxContainer/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "SPACE SHOP"
horizontal_alignment = 1

[node name="ScoreLabel" type="Label" parent="Control/ShopPanel/VBoxContainer/HeaderContainer"]
layout_mode = 2
text = "Score: 0"

[node name="CloseButton" type="Button" parent="Control/ShopPanel/VBoxContainer/HeaderContainer"]
custom_minimum_size = Vector2(80, 30)
layout_mode = 2
text = "CLOSE"

[node name="HSeparator" type="HSeparator" parent="Control/ShopPanel/VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="Control/ShopPanel/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ItemsContainer" type="VBoxContainer" parent="Control/ShopPanel/VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
